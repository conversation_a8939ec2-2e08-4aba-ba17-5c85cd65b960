'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { ArrowLeft, Mail, Linkedin, Instagram } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

export default function GeneralAffairsGroupPage() {
  const groupMembers = [
    {
      name: '謝舒安',
      role: '總務組組長',
      introduction: '負責總務組的整體規劃與財務管理，具有優秀的組織能力與責任感。擅長預算控制與資源配置，確保每個活動都能順利進行。',
      color: 'bg-emerald-500'
    },
    {
      name: '龔昀晴',
      role: '總務組組長',
      introduction: '專責餐飲安排與供應商聯繫，具有豐富的活動籌辦經驗。注重細節與品質，確保每位參與者都能享受到優質的服務。',
      color: 'bg-emerald-600'
    },
    {
      name: '劉峻成',
      role: '總務組組員',
      introduction: '負責原物料採購與庫存管理，具有敏銳的市場觀察力。善於尋找優質且經濟的供應來源，為團隊節省成本。',
      color: 'bg-teal-600'
    },
    {
      name: '黃翊棠',
      role: '總務組組員',
      introduction: '協助各項總務工作與後勤支援，做事認真負責且具有團隊精神。善於解決突發問題，是團隊中可靠的支柱。',
      color: 'bg-teal-700'
    }
  ]

  return (
    <div className="min-h-screen bg-cream">
      {/* Header */}
      <section className="bg-gradient-to-br from-green-50 via-cream to-green-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Button variant="outline" className="mb-6" asChild>
              <Link href="/about">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回關於我們
              </Link>
            </Button>
            
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                總務組
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                負責餐飲安排以及原物料訂購。
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Team Members */}
      <section className="py-20 bg-green-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              組員介紹
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              我們的總務組成員致力於維護組織的穩定運作和財務健康。
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {groupMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card hover className="h-full border-2 border-white">
                  <CardContent className="p-6 text-center">
                    {/* Avatar */}
                    <div className="w-24 h-24 rounded-full overflow-hidden mx-auto mb-4 border-2 border-orange-200">
                      <Image
                        src="/hssl_profile.jpg"
                        alt={member.name}
                        width={96}
                        height={96}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Info */}
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {member.name}
                    </h3>
                    <p className="text-orange-600 font-semibold mb-3">
                      {member.role}
                    </p>

                    {/* Introduction */}
                    <p className="text-gray-600 text-sm leading-relaxed mb-6">
                      {member.introduction}
                    </p>

                    {/* Social Links (placeholder) */}
                    <div className="flex justify-center space-x-3">
                      <button className="text-gray-400 hover:text-orange-600 transition-colors p-2">
                        <Mail className="w-5 h-5" />
                      </button>
                      <button className="text-gray-400 hover:text-orange-600 transition-colors p-2">
                        <Linkedin className="w-5 h-5" />
                      </button>
                      <button className="text-gray-400 hover:text-orange-600 transition-colors p-2">
                        <Instagram className="w-5 h-5" />
                      </button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
