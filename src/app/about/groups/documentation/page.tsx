'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { ArrowLeft, Mail, Linkedin, Instagram } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

export default function DocumentationGroupPage() {
  const groupMembers = [
    {
      name: '饒子儀',
      role: '文書美宣組組長',
      introduction: '負責社群媒體經營與品牌形象管理，具有敏銳的美感與創意思維。擅長內容策劃與視覺設計，致力於提升 HSSL 的知名度與影響力。',
      color: 'bg-green-500'
    },
    {
      name: '張育瑄',
      role: '文書美宣組組長',
      introduction: '專精於平面設計與海報製作，能夠創造出吸引人的視覺作品。注重細節與美感，確保每個宣傳素材都能達到專業水準。',
      color: 'bg-green-600'
    },
    {
      name: '林芸安',
      role: '文書美宣組組長',
      introduction: '負責文案撰寫與內容編輯，具有優秀的文字表達能力。善於將複雜的資訊轉化為易懂且有趣的內容，深受讀者喜愛。',
      color: 'bg-green-700'
    },
    {
      name: '林祐安',
      role: '文書美宣組組長',
      introduction: '專責 Instagram 經營與互動管理，了解社群媒體趨勢與用戶喜好。善於創造有趣的互動內容，提升粉絲參與度。',
      color: 'bg-green-800'
    },
    {
      name: '郭芃妘',
      role: '文書美宣組組員',
      introduction: '協助各項美宣工作與素材製作，學習態度積極且富有創意。善於團隊合作，是組內不可或缺的重要成員。',
      color: 'bg-green-900'
    }
  ]

  return (
    <div className="min-h-screen bg-cream">
      {/* Header */}
      <section className="bg-gradient-to-br from-green-50 via-cream to-green-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Button variant="outline" className="mb-6" asChild>
              <Link href="/about">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回關於我們
              </Link>
            </Button>
            
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                文書美宣組
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                負責經營臉書、IG帳號以及海報設計。
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Team Members */}
      <section className="py-20 bg-green-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              組員介紹
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              我們的文書美宣組成員致力於創造專業的視覺形象和有效的溝通。
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {groupMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card hover className="h-full border-2 border-white">
                  <CardContent className="p-6 text-center">
                    {/* Avatar */}
                    <div className="w-24 h-24 rounded-full overflow-hidden mx-auto mb-4 border-2 border-purple-200">
                      <Image
                        src="/hssl_profile.jpg"
                        alt={member.name}
                        width={96}
                        height={96}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Info */}
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {member.name}
                    </h3>
                    <p className="text-purple-600 font-semibold mb-3">
                      {member.role}
                    </p>

                    {/* Introduction */}
                    <p className="text-gray-600 text-sm leading-relaxed mb-6">
                      {member.introduction}
                    </p>

                    {/* Social Links (placeholder) */}
                    <div className="flex justify-center space-x-3">
                      <button className="text-gray-400 hover:text-purple-600 transition-colors p-2">
                        <Mail className="w-5 h-5" />
                      </button>
                      <button className="text-gray-400 hover:text-purple-600 transition-colors p-2">
                        <Linkedin className="w-5 h-5" />
                      </button>
                      <button className="text-gray-400 hover:text-purple-600 transition-colors p-2">
                        <Instagram className="w-5 h-5" />
                      </button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
